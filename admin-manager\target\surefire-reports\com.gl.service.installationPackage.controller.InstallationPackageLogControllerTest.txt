-------------------------------------------------------------------------------
Test set: com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 3.966 s <<< FAILURE! - in com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest
com.gl.service.installationPackage.controller.InstallationPackageLogControllerTest  Time elapsed: 3.965 s  <<< ERROR!
java.lang.IllegalStateException: Found multiple @SpringBootConfiguration annotated classes [Generic bean: class [com.gl.ManagerApplication]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [D:\yny\4g\admin\admin-manager\target\classes\com\gl\ManagerApplication.class], Generic bean: class [com.gl.AdminCoreApplication]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/com/gl/admin/admin-core/1.6.0/admin-core-1.6.0.jar!/com/gl/AdminCoreApplication.class], Generic bean: class [com.gl.CommonsApplication]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/com/gl/admin/admin-commons/1.6.0/admin-commons-1.6.0.jar!/com/gl/CommonsApplication.class]]

